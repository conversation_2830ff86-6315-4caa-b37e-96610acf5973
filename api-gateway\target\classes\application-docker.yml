spring:
  application:
    name: api-gateway
  devtools:
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
    livereload:
      enabled: true
    remote:
      secret: mysecret
  cloud:
    gateway:
      routes:
        - id: customer-service
          uri: http://customer-service:8085
          predicates:
            - Path=/api/customer/**
        - id: job-service
          uri: http://job-service:8086
          predicates:
            - Path=/api/job/**,/api/job-category/**
        - id: customer-contract-service
          uri: http://customer-contract-service:8087
          predicates:
            - Path=/api/customer-contract/**
        - id: customer-payment-service
          uri: http://customer-payment-service:8088
          predicates:
            - Path=/api/customer-payment/**
        - id: customer-statistics-service
          uri: http://customer-statistics-service:8089
          predicates:
            - Path=/api/customer-statistics/**
        - id: job-detail-service
          uri: http://customer-contract-service:8087
          predicates:
            - Path=/api/job-detail/**
        - id: work-shift-service
          uri: http://customer-contract-service:8087
          predicates:
            - Path=/api/work-shift/**
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
server:
  port: 8083

management:
  endpoints:
    web:
      exposure:
        include: "*"
