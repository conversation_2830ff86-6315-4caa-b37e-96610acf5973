# Sơ đồ lớp chi tiết cho Module Thống kê Khách hàng theo Doanh thu

## Tổng quan

Module thống kê khách hàng theo doanh thu cho phép quản lý xem thống kê doanh thu từ khách hàng trong một khoảng thời gian cụ thể. <PERSON><PERSON>le này sử dụng kiến trúc microservice, với các microservice sau tham gia:

1. **customer-statistics-service**: Dịch vụ chính xử lý thống kê doanh thu khách hàng
2. **customer-service**: <PERSON><PERSON> cấp thông tin về khách hàng
3. **customer-payment-service**: <PERSON><PERSON> cấp thông tin về các khoản thanh toán
4. **customer-contract-service**: Cung cấp thông tin về hợp đồng

## Sơ đồ lớp

```mermaid
classDiagram
    %% API Gateway
    class ApiGateway {
        +route()
    }

    %% Frontend
    class CustomerStatisticsComponent {
        -startDate: Date
        -endDate: Date
        +selectDateRange()
        +generateStatistics()
        +viewCustomerInvoices()
    }

    %% customer-statistics-service
    namespace customer-statistics-service {
        class CustomerStatisticsController {
            +getCustomerRevenueStatistics(startDate, endDate)
            +getCustomerInvoices(customerId, startDate, endDate)
            +healthCheck()
        }

        class CustomerStatisticsService {
            <<interface>>
            +getCustomerRevenueStatistics(startDate, endDate)
            +getCustomerInvoices(customerId, startDate, endDate)
        }

        class CustomerStatisticsServiceImpl {
            -customerClient: CustomerClient
            -paymentClient: CustomerPaymentClient
            -contractClient: CustomerContractClient
            +getCustomerRevenueStatistics(startDate, endDate)
            +getCustomerInvoices(customerId, startDate, endDate)
        }

        class CustomerRevenue {
            -id: Long
            -fullname: String
            -companyName: String
            -phoneNumber: String
            -email: String
            -address: String
            -contractCount: Integer
            -totalRevenue: Double
            -isDeleted: Boolean
            -createdAt: LocalDateTime
            -updatedAt: LocalDateTime
        }

        class CustomerClient {
            +getAllCustomers()
            +getCustomerById(id)
        }

        class CustomerPaymentClient {
            +getAllPayments()
            +getPaymentsByCustomerId(customerId)
            +getPaymentsByContractId(contractId)
        }

        class CustomerContractClient {
            +getContractsByDateRange(startDate, endDate)
            +getContractById(id)
            +getContractsByCustomerId(customerId)
        }
    }

    %% customer-service
    namespace customer-service {
        class Customer {
            -id: Long
            -fullname: String
            -companyName: String
            -phoneNumber: String
            -email: String
            -address: String
            -isDeleted: Boolean
            -createdAt: LocalDateTime
            -updatedAt: LocalDateTime
        }

        class CustomerController {
            +getCustomerById(id)
            +getAllCustomers()
            +createCustomer(customer)
            +updateCustomer(customer)
            +deleteCustomer(id)
            +checkCustomerExists(id)
            +searchCustomers(fullname, phoneNumber)
        }

        class CustomerService {
            <<interface>>
            +getCustomerById(id)
            +getAllCustomers()
            +createCustomer(customer)
            +updateCustomer(customer)
            +deleteCustomer(id)
            +checkCustomerExists(id)
            +searchCustomers(fullname, phoneNumber)
        }

        class CustomerServiceImpl {
            -customerRepository: CustomerRepository
            +getCustomerById(id)
            +getAllCustomers()
            +createCustomer(customer)
            +updateCustomer(customer)
            +deleteCustomer(id)
            +checkCustomerExists(id)
            +searchCustomers(fullname, phoneNumber)
        }

        class CustomerRepository {
            <<interface>>
            +findByIsDeletedFalse()
            +findByIdAndIsDeletedFalse(id)
            +existsByEmailAndIsDeletedFalse(email)
            +existsByPhoneNumberAndIsDeletedFalse(phoneNumber)
            +findByFullnameContainingIgnoreCaseAndIsDeletedFalse(fullname)
            +findByPhoneNumberContainingAndIsDeletedFalse(phoneNumber)
            +findByFullnameContainingIgnoreCaseAndPhoneNumberContainingAndIsDeletedFalse(fullname, phoneNumber)
        }
    }

    %% customer-payment-service
    namespace customer-payment-service {
        class CustomerPayment {
            -id: Long
            -paymentCode: String
            -paymentDate: LocalDateTime
            -paymentMethod: Integer
            -paymentAmount: Double
            -note: String
            -customerContractId: Long
            -customerId: Long
            -isDeleted: Boolean
            -createdAt: LocalDateTime
            -updatedAt: LocalDateTime
        }

        class CustomerPaymentController {
            +createPayment(payment)
            +getPaymentById(id)
            +getAllPayments()
            +getPaymentsByCustomerId(customerId)
            +getPaymentsByContractId(contractId)
            +searchCustomers(fullname, phoneNumber)
            +getActiveContractsByCustomerId(customerId)
            +getContractPaymentInfo(contractId)
            +getTotalPaidAmountByContractId(contractId)
            +getRemainingAmountByContractId(contractId)
        }

        class CustomerPaymentService {
            <<interface>>
            +createPayment(payment)
            +getPaymentById(id)
            +getAllPayments()
            +getPaymentsByCustomerId(customerId)
            +getPaymentsByContractId(contractId)
            +searchCustomers(fullname, phoneNumber)
            +getActiveContractsByCustomerId(customerId)
            +getContractPaymentInfo(contractId)
            +getTotalPaidAmountByContractId(contractId)
            +getRemainingAmountByContractId(contractId)
        }

        class CustomerPaymentServiceImpl {
            -paymentRepository: CustomerPaymentRepository
            -customerClient: CustomerClient
            -contractClient: CustomerContractClient
            +createPayment(payment)
            +getPaymentById(id)
            +getAllPayments()
            +getPaymentsByCustomerId(customerId)
            +getPaymentsByContractId(contractId)
            +searchCustomers(fullname, phoneNumber)
            +getActiveContractsByCustomerId(customerId)
            +getContractPaymentInfo(contractId)
            +getTotalPaidAmountByContractId(contractId)
            +getRemainingAmountByContractId(contractId)
        }

        class CustomerPaymentRepository {
            <<interface>>
            +findByIsDeletedFalse()
            +findByIdAndIsDeletedFalse(id)
            +findByCustomerIdAndIsDeletedFalse(customerId)
            +findByCustomerContractIdAndIsDeletedFalse(contractId)
            +getTotalPaidAmountByContractId(contractId)
            +existsByPaymentCodeAndIsDeletedFalse(paymentCode)
        }
    }

    %% customer-contract-service
    namespace customer-contract-service {
        class CustomerContract {
            -id: Long
            -contractCode: String
            -startingDate: LocalDate
            -endingDate: LocalDate
            -signedDate: LocalDate
            -numberOfWorkers: Integer
            -totalAmount: Double
            -totalPaid: Double
            -address: String
            -description: String
            -jobCategoryId: Long
            -customerId: Long
            -status: Integer
            -isDeleted: Boolean
            -createdAt: LocalDateTime
            -updatedAt: LocalDateTime
        }

        class CustomerContractController {
            +createContract(contract)
            +updateContract(contract)
            +deleteContract(id)
            +getContractById(id)
            +getAllContracts()
            +getContractsByCustomerId(customerId)
            +getContractsByStatus(status)
            +getContractsByDateRange(startDate, endDate)
            +getContractsByJobCategoryId(jobCategoryId)
            +updateContractStatus(id, status)
            +signContract(id, signedDate)
            +checkContractExists(id)
        }

        class CustomerContractService {
            <<interface>>
            +createContract(contract)
            +updateContract(contract)
            +deleteContract(id)
            +getContractById(id)
            +getAllContracts()
            +getContractsByCustomerId(customerId)
            +getContractsByStatus(status)
            +getContractsByDateRange(startDate, endDate)
            +getContractsByJobCategoryId(jobCategoryId)
            +updateContractStatus(id, status)
            +signContract(id, signedDate)
            +checkContractExists(id)
        }

        class CustomerContractServiceImpl {
            -contractRepository: CustomerContractRepository
            -customerClient: CustomerClient
            -jobCategoryClient: JobCategoryClient
            +createContract(contract)
            +updateContract(contract)
            +deleteContract(id)
            +getContractById(id)
            +getAllContracts()
            +getContractsByCustomerId(customerId)
            +getContractsByStatus(status)
            +getContractsByDateRange(startDate, endDate)
            +getContractsByJobCategoryId(jobCategoryId)
            +updateContractStatus(id, status)
            +signContract(id, signedDate)
            +checkContractExists(id)
        }

        class CustomerContractRepository {
            <<interface>>
            +findByIsDeletedFalse()
            +findByIdAndIsDeletedFalse(id)
            +findByCustomerIdAndIsDeletedFalse(customerId)
            +findByStatusAndIsDeletedFalse(status)
            +findByStartingDateBetweenAndIsDeletedFalse(startDate, endDate)
            +findByJobCategoryIdAndIsDeletedFalse(jobCategoryId)
            +existsByContractCodeAndIsDeletedFalse(contractCode)
            +existsByContractCodeAndIsDeletedFalseAndIdNot(contractCode, id)
        }
    }

    %% Relationships
    ApiGateway --> CustomerStatisticsController
    CustomerStatisticsComponent --> ApiGateway
    
    CustomerStatisticsController --> CustomerStatisticsService
    CustomerStatisticsService <|.. CustomerStatisticsServiceImpl
    
    CustomerStatisticsServiceImpl --> CustomerClient
    CustomerStatisticsServiceImpl --> CustomerPaymentClient
    CustomerStatisticsServiceImpl --> CustomerContractClient
    
    CustomerStatisticsServiceImpl --> CustomerRevenue
    
    CustomerClient --> CustomerController
    CustomerPaymentClient --> CustomerPaymentController
    CustomerContractClient --> CustomerContractController
    
    CustomerRevenue --|> Customer
    
    CustomerController --> CustomerService
    CustomerService <|.. CustomerServiceImpl
    CustomerServiceImpl --> CustomerRepository
    
    CustomerPaymentController --> CustomerPaymentService
    CustomerPaymentService <|.. CustomerPaymentServiceImpl
    CustomerPaymentServiceImpl --> CustomerPaymentRepository
    
    CustomerContractController --> CustomerContractService
    CustomerContractService <|.. CustomerContractServiceImpl
    CustomerContractServiceImpl --> CustomerContractRepository
```

## Giải thích chi tiết các thành phần

### 1. Frontend
- **CustomerStatisticsComponent**: Component frontend cho phép người dùng chọn khoảng thời gian và hiển thị kết quả thống kê.

### 2. API Gateway
- **ApiGateway**: Cổng vào duy nhất cho tất cả các request từ frontend đến các microservice.

### 3. customer-statistics-service
- **CustomerStatisticsController**: Controller xử lý các request liên quan đến thống kê khách hàng.
  - `getCustomerRevenueStatistics(startDate, endDate)`: Lấy thống kê doanh thu theo khách hàng trong khoảng thời gian.
  - `getCustomerInvoices(customerId, startDate, endDate)`: Lấy danh sách hóa đơn của khách hàng trong khoảng thời gian.
  - `healthCheck()`: Kiểm tra trạng thái hoạt động của service.

- **CustomerStatisticsService**: Interface định nghĩa các phương thức xử lý logic nghiệp vụ liên quan đến thống kê.
  - `getCustomerRevenueStatistics(startDate, endDate)`: Lấy thống kê doanh thu theo khách hàng trong khoảng thời gian.
  - `getCustomerInvoices(customerId, startDate, endDate)`: Lấy danh sách hóa đơn của khách hàng trong khoảng thời gian.

- **CustomerStatisticsServiceImpl**: Lớp triển khai CustomerStatisticsService, thực hiện các phương thức xử lý logic nghiệp vụ.
  - `customerClient`: Feign client gọi đến customer-service.
  - `paymentClient`: Feign client gọi đến customer-payment-service.
  - `contractClient`: Feign client gọi đến customer-contract-service.

- **CustomerRevenue**: Model kế thừa từ Customer, chứa thêm thông tin thống kê doanh thu.
  - `contractCount`: Số lượng hợp đồng đã ký.
  - `totalRevenue`: Tổng doanh thu từ khách hàng.

- **CustomerClient**: Feign client gọi đến customer-service.
  - `getAllCustomers()`: Lấy danh sách tất cả khách hàng.
  - `getCustomerById(id)`: Lấy thông tin khách hàng theo ID.

- **CustomerPaymentClient**: Feign client gọi đến customer-payment-service.
  - `getAllPayments()`: Lấy danh sách tất cả thanh toán.
  - `getPaymentsByCustomerId(customerId)`: Lấy danh sách thanh toán theo ID khách hàng.
  - `getPaymentsByContractId(contractId)`: Lấy danh sách thanh toán theo ID hợp đồng.

- **CustomerContractClient**: Feign client gọi đến customer-contract-service.
  - `getContractsByDateRange(startDate, endDate)`: Lấy danh sách hợp đồng trong khoảng thời gian.
  - `getContractById(id)`: Lấy thông tin hợp đồng theo ID.
  - `getContractsByCustomerId(customerId)`: Lấy danh sách hợp đồng theo ID khách hàng.

### 4. customer-service
- **Customer**: Entity chứa thông tin khách hàng.
  - `id`: ID khách hàng.
  - `fullname`: Tên khách hàng.
  - `companyName`: Tên doanh nghiệp.
  - `phoneNumber`: Số điện thoại.
  - `email`: Email.
  - `address`: Địa chỉ.
  - `isDeleted`: Đánh dấu đã xóa.
  - `createdAt`: Thời gian tạo.
  - `updatedAt`: Thời gian cập nhật.

- **CustomerController**: Controller xử lý các request liên quan đến khách hàng.
- **CustomerService**: Interface định nghĩa các phương thức xử lý logic nghiệp vụ liên quan đến khách hàng.
- **CustomerServiceImpl**: Lớp triển khai CustomerService.
- **CustomerRepository**: Interface truy cập dữ liệu khách hàng.

### 5. customer-payment-service
- **CustomerPayment**: Entity chứa thông tin thanh toán.
  - `id`: ID thanh toán.
  - `paymentCode`: Mã thanh toán.
  - `paymentDate`: Ngày thanh toán.
  - `paymentMethod`: Phương thức thanh toán.
  - `paymentAmount`: Số tiền thanh toán.
  - `note`: Ghi chú.
  - `customerContractId`: ID hợp đồng.
  - `customerId`: ID khách hàng.
  - `isDeleted`: Đánh dấu đã xóa.
  - `createdAt`: Thời gian tạo.
  - `updatedAt`: Thời gian cập nhật.

- **CustomerPaymentController**: Controller xử lý các request liên quan đến thanh toán.
- **CustomerPaymentService**: Interface định nghĩa các phương thức xử lý logic nghiệp vụ liên quan đến thanh toán.
- **CustomerPaymentServiceImpl**: Lớp triển khai CustomerPaymentService.
- **CustomerPaymentRepository**: Interface truy cập dữ liệu thanh toán.

### 6. customer-contract-service
- **CustomerContract**: Entity chứa thông tin hợp đồng.
  - `id`: ID hợp đồng.
  - `contractCode`: Mã hợp đồng.
  - `startingDate`: Ngày bắt đầu.
  - `endingDate`: Ngày kết thúc.
  - `signedDate`: Ngày ký.
  - `numberOfWorkers`: Số lượng nhân công.
  - `totalAmount`: Tổng giá trị hợp đồng.
  - `totalPaid`: Tổng số tiền đã thanh toán.
  - `address`: Địa chỉ làm việc.
  - `description`: Mô tả công việc.
  - `jobCategoryId`: ID loại công việc.
  - `customerId`: ID khách hàng.
  - `status`: Trạng thái hợp đồng.
  - `isDeleted`: Đánh dấu đã xóa.
  - `createdAt`: Thời gian tạo.
  - `updatedAt`: Thời gian cập nhật.

- **CustomerContractController**: Controller xử lý các request liên quan đến hợp đồng.
- **CustomerContractService**: Interface định nghĩa các phương thức xử lý logic nghiệp vụ liên quan đến hợp đồng.
- **CustomerContractServiceImpl**: Lớp triển khai CustomerContractService.
- **CustomerContractRepository**: Interface truy cập dữ liệu hợp đồng.

## Luồng hoạt động

1. Quản lý chọn chức năng thống kê khách hàng theo doanh thu
2. Hệ thống hiển thị giao diện thống kê với form chọn khoảng thời gian
3. Quản lý chọn khoảng thời gian và bấm nút thống kê
4. Frontend gửi request đến API Gateway
5. API Gateway định tuyến request đến CustomerStatisticsController
6. CustomerStatisticsController gọi phương thức getCustomerRevenueStatistics() của CustomerStatisticsService
7. CustomerStatisticsServiceImpl thực hiện các bước sau:
   - Lấy danh sách khách hàng từ CustomerClient
   - Lấy danh sách hợp đồng trong khoảng thời gian từ CustomerContractClient
   - Lấy danh sách thanh toán từ CustomerPaymentClient
   - Tính toán số lượng hợp đồng và tổng doanh thu cho mỗi khách hàng
   - Sắp xếp kết quả theo tổng doanh thu từ cao đến thấp
8. Kết quả được trả về qua các lớp trung gian, thông qua API Gateway đến Frontend
9. Frontend hiển thị danh sách khách hàng với thông tin thống kê
10. Khi quản lý click vào một khách hàng:
    - Frontend gửi request đến API Gateway để lấy danh sách hóa đơn của khách hàng đó
    - CustomerStatisticsController gọi phương thức getCustomerInvoices() của CustomerStatisticsService
    - CustomerStatisticsServiceImpl lấy danh sách thanh toán của khách hàng từ CustomerPaymentClient
    - Kết quả được trả về và hiển thị trên Frontend

## Lưu ý về kiến trúc microservice

- **customer-statistics-service** không có cơ sở dữ liệu riêng mà chỉ tổng hợp dữ liệu từ các service khác
- Giao tiếp giữa các service thông qua REST API sử dụng Feign Client
- API Gateway đóng vai trò trung gian giữa Frontend và các microservice
- CustomerRevenue kế thừa từ Customer để tái sử dụng các thuộc tính của Customer và thêm các thuộc tính thống kê
