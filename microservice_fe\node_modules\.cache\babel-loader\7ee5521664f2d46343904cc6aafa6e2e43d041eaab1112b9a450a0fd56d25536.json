{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\components\\\\contract\\\\WorkingDatesPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Accordion, AccordionSummary, AccordionDetails, Chip, Grid, useTheme, Divider } from '@mui/material';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport CalendarMonthIcon from '@mui/icons-material/CalendarMonth';\nimport EventIcon from '@mui/icons-material/Event';\nimport { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';\nimport { formatCurrency } from '../../utils/formatters';\n\n// Mapping for Vietnamese day names\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst dayNames = {\n  1: 'Th<PERSON>',\n  2: '<PERSON><PERSON><PERSON> <PERSON>',\n  3: '<PERSON><PERSON><PERSON>ư',\n  4: '<PERSON><PERSON><PERSON>',\n  5: '<PERSON><PERSON><PERSON>',\n  6: 'Th<PERSON> Bảy',\n  7: 'Chủ Nhật'\n};\nconst WorkingDatesPreview = ({\n  workShift,\n  jobDetail,\n  shiftIndex\n}) => {\n  _s();\n  const [expanded, setExpanded] = useState(false);\n  const theme = useTheme();\n\n  // Calculate working dates for this shift\n  const workingDates = calculateWorkingDates(jobDetail.startDate, jobDetail.endDate, workShift.workingDays);\n\n  // Group dates by day of week for better display\n  const datesByDayOfWeek = {};\n  workingDates.forEach(dateStr => {\n    const date = new Date(dateStr.split('/').reverse().join('-')); // Convert DD/MM/YYYY to YYYY-MM-DD\n    const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7\n    if (!datesByDayOfWeek[dayOfWeek]) {\n      datesByDayOfWeek[dayOfWeek] = [];\n    }\n    datesByDayOfWeek[dayOfWeek].push(dateStr);\n  });\n\n  // Calculate total amount for this shift\n  const totalAmount = workShift.salary && workShift.numberOfWorkers && workingDates.length ? workShift.salary * workShift.numberOfWorkers * workingDates.length : 0;\n  if (!workShift.workingDays || !jobDetail.startDate || !jobDetail.endDate) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      sx: {\n        mb: 2,\n        borderColor: theme.palette.warning.light\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Vui l\\xF2ng ch\\u1ECDn ng\\xE0y l\\xE0m vi\\u1EC7c v\\xE0 th\\u1EDDi gian \\u0111\\u1EC3 xem l\\u1ECBch l\\xE0m vi\\u1EC7c chi ti\\u1EBFt\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    variant: \"outlined\",\n    sx: {\n      mb: 2,\n      borderColor: theme.palette.primary.light\n    },\n    children: /*#__PURE__*/_jsxDEV(CardContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CalendarMonthIcon, {\n          sx: {\n            mr: 1,\n            color: theme.palette.primary.main\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 'bold',\n            color: theme.palette.primary.main\n          },\n          children: [\"L\\u1ECBch l\\xE0m vi\\u1EC7c Ca \", shiftIndex + 1, \" (\", workShift.startTime, \" - \", workShift.endTime, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Ng\\xE0y l\\xE0m vi\\u1EC7c\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: formatWorkingDays(workShift.workingDays)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"T\\u1ED5ng s\\u1ED1 ng\\xE0y\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium',\n              color: theme.palette.primary.main\n            },\n            children: [workingDates.length, \" ng\\xE0y\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"S\\u1ED1 nh\\xE2n c\\xF4ng\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'medium'\n            },\n            children: [workShift.numberOfWorkers, \" ng\\u01B0\\u1EDDi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"T\\u1ED5ng ti\\u1EC1n ca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 'bold',\n              color: theme.palette.success.main\n            },\n            children: formatCurrency(totalAmount)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        expanded: expanded,\n        onChange: () => setExpanded(!expanded),\n        sx: {\n          boxShadow: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 25\n          }, this),\n          sx: {\n            backgroundColor: theme.palette.action.hover,\n            borderRadius: '4px',\n            minHeight: '36px',\n            '& .MuiAccordionSummary-content': {\n              margin: '4px 0'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(EventIcon, {\n              fontSize: \"small\",\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"L\\u1ECBch l\\xE0m vi\\u1EC7c chi ti\\u1EBFt (\", workingDates.length, \" ng\\xE0y)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          sx: {\n            pt: 2\n          },\n          children: [Object.entries(datesByDayOfWeek).sort(([a], [b]) => parseInt(a) - parseInt(b)).map(([dayOfWeek, dates]) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 'bold',\n                color: 'primary.main',\n                mb: 1\n              },\n              children: [dayNames[parseInt(dayOfWeek)], \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 1\n              },\n              children: dates.map((date, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: date,\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"primary\",\n                sx: {\n                  fontSize: '0.75rem'\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this)]\n          }, dayOfWeek, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this)), workingDates.length === 0 && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              fontStyle: 'italic'\n            },\n            children: \"Kh\\xF4ng c\\xF3 ng\\xE0y l\\xE0m vi\\u1EC7c n\\xE0o trong kho\\u1EA3ng th\\u1EDDi gian \\u0111\\xE3 ch\\u1ECDn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), totalAmount > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          p: 2,\n          backgroundColor: theme.palette.success.light,\n          borderRadius: '4px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 'bold',\n            mb: 1\n          },\n          children: \"T\\xEDnh to\\xE1n chi ti\\u1EBFt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [formatCurrency(workShift.salary || 0), \" \\xD7 \", workShift.numberOfWorkers, \" ng\\u01B0\\u1EDDi \\xD7 \", workingDates.length, \" ng\\xE0y = \", formatCurrency(totalAmount)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkingDatesPreview, \"8cGCrqUgWwWUmDt/dmtvzNbRsQU=\", false, function () {\n  return [useTheme];\n});\n_c = WorkingDatesPreview;\nexport default WorkingDatesPreview;\nvar _c;\n$RefreshReg$(_c, \"WorkingDatesPreview\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "Grid", "useTheme", "Divider", "ExpandMoreIcon", "CalendarMonthIcon", "EventIcon", "calculateWorkingDates", "formatWorkingDays", "formatCurrency", "jsxDEV", "_jsxDEV", "dayNames", "WorkingDatesPreview", "workShift", "jobDetail", "shiftIndex", "_s", "expanded", "setExpanded", "theme", "workingDates", "startDate", "endDate", "workingDays", "datesByDayOfWeek", "for<PERSON>ach", "dateStr", "date", "Date", "split", "reverse", "join", "dayOfWeek", "getDay", "push", "totalAmount", "salary", "numberOfWorkers", "length", "variant", "sx", "mb", "borderColor", "palette", "warning", "light", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "display", "alignItems", "mr", "main", "fontWeight", "startTime", "endTime", "container", "spacing", "item", "xs", "sm", "success", "onChange", "boxShadow", "expandIcon", "backgroundColor", "action", "hover", "borderRadius", "minHeight", "margin", "fontSize", "pt", "Object", "entries", "sort", "a", "b", "parseInt", "map", "dates", "flexWrap", "gap", "index", "label", "size", "fontStyle", "mt", "p", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/WorkingDatesPreview.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Chip,\n  Grid,\n  useTheme,\n  Divider,\n} from '@mui/material';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport CalendarMonthIcon from '@mui/icons-material/CalendarMonth';\nimport EventIcon from '@mui/icons-material/Event';\nimport { WorkShift, JobDetail } from '../../models';\nimport { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';\nimport { formatCurrency } from '../../utils/formatters';\n\n// Mapping for Vietnamese day names\nconst dayNames: { [key: number]: string } = {\n  1: 'Thứ <PERSON>',\n  2: 'Thứ <PERSON>',\n  3: 'Th<PERSON>ư',\n  4: 'Th<PERSON>',\n  5: 'Th<PERSON>',\n  6: 'Th<PERSON>',\n  7: 'Ch<PERSON>'\n};\n\ninterface WorkingDatesPreviewProps {\n  workShift: WorkShift;\n  jobDetail: JobDetail;\n  shiftIndex: number;\n}\n\nconst WorkingDatesPreview: React.FC<WorkingDatesPreviewProps> = ({\n  workShift,\n  jobDetail,\n  shiftIndex\n}) => {\n  const [expanded, setExpanded] = useState(false);\n  const theme = useTheme();\n\n  // Calculate working dates for this shift\n  const workingDates = calculateWorkingDates(\n    jobDetail.startDate,\n    jobDetail.endDate,\n    workShift.workingDays\n  );\n\n  // Group dates by day of week for better display\n  const datesByDayOfWeek: { [key: number]: string[] } = {};\n  workingDates.forEach(dateStr => {\n    const date = new Date(dateStr.split('/').reverse().join('-')); // Convert DD/MM/YYYY to YYYY-MM-DD\n    const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7\n    if (!datesByDayOfWeek[dayOfWeek]) {\n      datesByDayOfWeek[dayOfWeek] = [];\n    }\n    datesByDayOfWeek[dayOfWeek].push(dateStr);\n  });\n\n  // Calculate total amount for this shift\n  const totalAmount = workShift.salary && workShift.numberOfWorkers && workingDates.length\n    ? workShift.salary * workShift.numberOfWorkers * workingDates.length\n    : 0;\n\n  if (!workShift.workingDays || !jobDetail.startDate || !jobDetail.endDate) {\n    return (\n      <Card variant=\"outlined\" sx={{ mb: 2, borderColor: theme.palette.warning.light }}>\n        <CardContent>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Vui lòng chọn ngày làm việc và thời gian để xem lịch làm việc chi tiết\n          </Typography>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card variant=\"outlined\" sx={{ mb: 2, borderColor: theme.palette.primary.light }}>\n      <CardContent>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n          <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main }} />\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>\n            Lịch làm việc Ca {shiftIndex + 1} ({workShift.startTime} - {workShift.endTime})\n          </Typography>\n        </Box>\n\n        {/* Summary Information */}\n        <Grid container spacing={2} sx={{ mb: 2 }}>\n          <Grid item xs={6} sm={3}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Ngày làm việc</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {formatWorkingDays(workShift.workingDays)}\n            </Typography>\n          </Grid>\n          <Grid item xs={6} sm={3}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Tổng số ngày</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium', color: theme.palette.primary.main }}>\n              {workingDates.length} ngày\n            </Typography>\n          </Grid>\n          <Grid item xs={6} sm={3}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Số nhân công</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'medium' }}>\n              {workShift.numberOfWorkers} người\n            </Typography>\n          </Grid>\n          <Grid item xs={6} sm={3}>\n            <Typography variant=\"body2\" color=\"text.secondary\">Tổng tiền ca</Typography>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>\n              {formatCurrency(totalAmount)}\n            </Typography>\n          </Grid>\n        </Grid>\n\n        <Divider sx={{ mb: 2 }} />\n\n        {/* Detailed working dates */}\n        <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)} sx={{ boxShadow: 'none' }}>\n          <AccordionSummary\n            expandIcon={<ExpandMoreIcon />}\n            sx={{\n              backgroundColor: theme.palette.action.hover,\n              borderRadius: '4px',\n              minHeight: '36px',\n              '& .MuiAccordionSummary-content': { margin: '4px 0' }\n            }}\n          >\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <EventIcon fontSize=\"small\" sx={{ mr: 1 }} />\n              <Typography variant=\"body2\">\n                Lịch làm việc chi tiết ({workingDates.length} ngày)\n              </Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails sx={{ pt: 2 }}>\n            {Object.entries(datesByDayOfWeek)\n              .sort(([a], [b]) => parseInt(a) - parseInt(b))\n              .map(([dayOfWeek, dates]) => (\n                <Box key={dayOfWeek} sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>\n                    {dayNames[parseInt(dayOfWeek)]}:\n                  </Typography>\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                    {dates.map((date, index) => (\n                      <Chip\n                        key={index}\n                        label={date}\n                        size=\"small\"\n                        variant=\"outlined\"\n                        color=\"primary\"\n                        sx={{ fontSize: '0.75rem' }}\n                      />\n                    ))}\n                  </Box>\n                </Box>\n              ))}\n\n            {workingDates.length === 0 && (\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontStyle: 'italic' }}>\n                Không có ngày làm việc nào trong khoảng thời gian đã chọn\n              </Typography>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Calculation breakdown */}\n        {totalAmount > 0 && (\n          <Box sx={{ mt: 2, p: 2, backgroundColor: theme.palette.success.light, borderRadius: '4px' }}>\n            <Typography variant=\"body2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              Tính toán chi tiết:\n            </Typography>\n            <Typography variant=\"body2\">\n              {formatCurrency(workShift.salary || 0)} × {workShift.numberOfWorkers} người × {workingDates.length} ngày = {formatCurrency(totalAmount)}\n            </Typography>\n          </Box>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default WorkingDatesPreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,SAASC,qBAAqB,EAAEC,iBAAiB,QAAQ,8BAA8B;AACvF,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAmC,GAAG;EAC1C,CAAC,EAAE,SAAS;EACZ,CAAC,EAAE,QAAQ;EACX,CAAC,EAAE,QAAQ;EACX,CAAC,EAAE,SAAS;EACZ,CAAC,EAAE,SAAS;EACZ,CAAC,EAAE,SAAS;EACZ,CAAC,EAAE;AACL,CAAC;AAQD,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,SAAS;EACTC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM4B,KAAK,GAAGlB,QAAQ,CAAC,CAAC;;EAExB;EACA,MAAMmB,YAAY,GAAGd,qBAAqB,CACxCQ,SAAS,CAACO,SAAS,EACnBP,SAAS,CAACQ,OAAO,EACjBT,SAAS,CAACU,WACZ,CAAC;;EAED;EACA,MAAMC,gBAA6C,GAAG,CAAC,CAAC;EACxDJ,YAAY,CAACK,OAAO,CAACC,OAAO,IAAI;IAC9B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAMC,SAAS,GAAGL,IAAI,CAACM,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGN,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACT,gBAAgB,CAACQ,SAAS,CAAC,EAAE;MAChCR,gBAAgB,CAACQ,SAAS,CAAC,GAAG,EAAE;IAClC;IACAR,gBAAgB,CAACQ,SAAS,CAAC,CAACE,IAAI,CAACR,OAAO,CAAC;EAC3C,CAAC,CAAC;;EAEF;EACA,MAAMS,WAAW,GAAGtB,SAAS,CAACuB,MAAM,IAAIvB,SAAS,CAACwB,eAAe,IAAIjB,YAAY,CAACkB,MAAM,GACpFzB,SAAS,CAACuB,MAAM,GAAGvB,SAAS,CAACwB,eAAe,GAAGjB,YAAY,CAACkB,MAAM,GAClE,CAAC;EAEL,IAAI,CAACzB,SAAS,CAACU,WAAW,IAAI,CAACT,SAAS,CAACO,SAAS,IAAI,CAACP,SAAS,CAACQ,OAAO,EAAE;IACxE,oBACEZ,OAAA,CAAChB,IAAI;MAAC6C,OAAO,EAAC,UAAU;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,WAAW,EAAEvB,KAAK,CAACwB,OAAO,CAACC,OAAO,CAACC;MAAM,CAAE;MAAAC,QAAA,eAC/EpC,OAAA,CAACf,WAAW;QAAAmD,QAAA,eACVpC,OAAA,CAACjB,UAAU;UAAC8C,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACEzC,OAAA,CAAChB,IAAI;IAAC6C,OAAO,EAAC,UAAU;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,WAAW,EAAEvB,KAAK,CAACwB,OAAO,CAACS,OAAO,CAACP;IAAM,CAAE;IAAAC,QAAA,eAC/EpC,OAAA,CAACf,WAAW;MAAAmD,QAAA,gBACVpC,OAAA,CAAClB,GAAG;QAACgD,EAAE,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEb,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACxDpC,OAAA,CAACN,iBAAiB;UAACoC,EAAE,EAAE;YAAEe,EAAE,EAAE,CAAC;YAAER,KAAK,EAAE5B,KAAK,CAACwB,OAAO,CAACS,OAAO,CAACI;UAAK;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEzC,OAAA,CAACjB,UAAU;UAAC8C,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEiB,UAAU,EAAE,MAAM;YAAEV,KAAK,EAAE5B,KAAK,CAACwB,OAAO,CAACS,OAAO,CAACI;UAAK,CAAE;UAAAV,QAAA,GAAC,gCAC5E,EAAC/B,UAAU,GAAG,CAAC,EAAC,IAAE,EAACF,SAAS,CAAC6C,SAAS,EAAC,KAAG,EAAC7C,SAAS,CAAC8C,OAAO,EAAC,GAChF;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNzC,OAAA,CAACV,IAAI;QAAC4D,SAAS;QAACC,OAAO,EAAE,CAAE;QAACrB,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACxCpC,OAAA,CAACV,IAAI;UAAC8D,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACtBpC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7EzC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEiB,UAAU,EAAE;YAAS,CAAE;YAAAX,QAAA,EACtDvC,iBAAiB,CAACM,SAAS,CAACU,WAAW;UAAC;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPzC,OAAA,CAACV,IAAI;UAAC8D,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACtBpC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EzC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEiB,UAAU,EAAE,QAAQ;cAAEV,KAAK,EAAE5B,KAAK,CAACwB,OAAO,CAACS,OAAO,CAACI;YAAK,CAAE;YAAAV,QAAA,GACzF1B,YAAY,CAACkB,MAAM,EAAC,UACvB;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPzC,OAAA,CAACV,IAAI;UAAC8D,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACtBpC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EzC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEiB,UAAU,EAAE;YAAS,CAAE;YAAAX,QAAA,GACtDjC,SAAS,CAACwB,eAAe,EAAC,kBAC7B;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPzC,OAAA,CAACV,IAAI;UAAC8D,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACtBpC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EzC,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEiB,UAAU,EAAE,MAAM;cAAEV,KAAK,EAAE5B,KAAK,CAACwB,OAAO,CAACsB,OAAO,CAACT;YAAK,CAAE;YAAAV,QAAA,EACvFtC,cAAc,CAAC2B,WAAW;UAAC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPzC,OAAA,CAACR,OAAO;QAACsC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG1BzC,OAAA,CAACd,SAAS;QAACqB,QAAQ,EAAEA,QAAS;QAACiD,QAAQ,EAAEA,CAAA,KAAMhD,WAAW,CAAC,CAACD,QAAQ,CAAE;QAACuB,EAAE,EAAE;UAAE2B,SAAS,EAAE;QAAO,CAAE;QAAArB,QAAA,gBAC/FpC,OAAA,CAACb,gBAAgB;UACfuE,UAAU,eAAE1D,OAAA,CAACP,cAAc;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC/BX,EAAE,EAAE;YACF6B,eAAe,EAAElD,KAAK,CAACwB,OAAO,CAAC2B,MAAM,CAACC,KAAK;YAC3CC,YAAY,EAAE,KAAK;YACnBC,SAAS,EAAE,MAAM;YACjB,gCAAgC,EAAE;cAAEC,MAAM,EAAE;YAAQ;UACtD,CAAE;UAAA5B,QAAA,eAEFpC,OAAA,CAAClB,GAAG;YAACgD,EAAE,EAAE;cAAEa,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACjDpC,OAAA,CAACL,SAAS;cAACsE,QAAQ,EAAC,OAAO;cAACnC,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CzC,OAAA,CAACjB,UAAU;cAAC8C,OAAO,EAAC,OAAO;cAAAO,QAAA,GAAC,4CACF,EAAC1B,YAAY,CAACkB,MAAM,EAAC,WAC/C;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnBzC,OAAA,CAACZ,gBAAgB;UAAC0C,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA9B,QAAA,GAC7B+B,MAAM,CAACC,OAAO,CAACtD,gBAAgB,CAAC,CAC9BuD,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKC,QAAQ,CAACF,CAAC,CAAC,GAAGE,QAAQ,CAACD,CAAC,CAAC,CAAC,CAC7CE,GAAG,CAAC,CAAC,CAACnD,SAAS,EAAEoD,KAAK,CAAC,kBACtB1E,OAAA,CAAClB,GAAG;YAAiBgD,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAK,QAAA,gBACjCpC,OAAA,CAACjB,UAAU;cAAC8C,OAAO,EAAC,OAAO;cAACC,EAAE,EAAE;gBAAEiB,UAAU,EAAE,MAAM;gBAAEV,KAAK,EAAE,cAAc;gBAAEN,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,GAClFnC,QAAQ,CAACuE,QAAQ,CAAClD,SAAS,CAAC,CAAC,EAAC,GACjC;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzC,OAAA,CAAClB,GAAG;cAACgD,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEgC,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAxC,QAAA,EACpDsC,KAAK,CAACD,GAAG,CAAC,CAACxD,IAAI,EAAE4D,KAAK,kBACrB7E,OAAA,CAACX,IAAI;gBAEHyF,KAAK,EAAE7D,IAAK;gBACZ8D,IAAI,EAAC,OAAO;gBACZlD,OAAO,EAAC,UAAU;gBAClBQ,KAAK,EAAC,SAAS;gBACfP,EAAE,EAAE;kBAAEmC,QAAQ,EAAE;gBAAU;cAAE,GALvBY,KAAK;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAfEnB,SAAS;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBd,CACN,CAAC,EAEH/B,YAAY,CAACkB,MAAM,KAAK,CAAC,iBACxB5B,OAAA,CAACjB,UAAU;YAAC8C,OAAO,EAAC,OAAO;YAACQ,KAAK,EAAC,gBAAgB;YAACP,EAAE,EAAE;cAAEkD,SAAS,EAAE;YAAS,CAAE;YAAA5C,QAAA,EAAC;UAEhF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAGXhB,WAAW,GAAG,CAAC,iBACdzB,OAAA,CAAClB,GAAG;QAACgD,EAAE,EAAE;UAAEmD,EAAE,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEvB,eAAe,EAAElD,KAAK,CAACwB,OAAO,CAACsB,OAAO,CAACpB,KAAK;UAAE2B,YAAY,EAAE;QAAM,CAAE;QAAA1B,QAAA,gBAC1FpC,OAAA,CAACjB,UAAU;UAAC8C,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEiB,UAAU,EAAE,MAAM;YAAEhB,EAAE,EAAE;UAAE,CAAE;UAAAK,QAAA,EAAC;QAE/D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA,CAACjB,UAAU;UAAC8C,OAAO,EAAC,OAAO;UAAAO,QAAA,GACxBtC,cAAc,CAACK,SAAS,CAACuB,MAAM,IAAI,CAAC,CAAC,EAAC,QAAG,EAACvB,SAAS,CAACwB,eAAe,EAAC,wBAAS,EAACjB,YAAY,CAACkB,MAAM,EAAC,aAAQ,EAAC9B,cAAc,CAAC2B,WAAW,CAAC;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7H,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACnC,EAAA,CAlJIJ,mBAAuD;EAAA,QAM7CX,QAAQ;AAAA;AAAA4F,EAAA,GANlBjF,mBAAuD;AAoJ7D,eAAeA,mBAAmB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}