{"ast": null, "code": "var _jsxFileName = \"D:\\\\HeThongCongTyQuanLyNhanCong\\\\Microservice_With_Kubernetes\\\\microservice_fe\\\\src\\\\pages\\\\CreateContractPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateContractPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [contract, setContract] = useState({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    address: '',\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const handleContractChange = updatedContract => {\n    setContract(updatedContract);\n  };\n  const validateContract = () => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n\n    if (!contract.address) {\n      setError('Vui lòng nhập địa chỉ hợp đồng');\n      return false;\n    }\n\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.workLocation) {\n        setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n        return false;\n      }\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n    return true;\n  };\n  const handleSubmit = async () => {\n    setError(null);\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n    setLoading(true);\n    try {\n      const createdContract = await contractService.createContract(contractToSubmit);\n      setSuccess('Hợp đồng đã được tạo thành công!');\n\n      // Redirect to the contract details page after a short delay\n      setTimeout(() => {\n        navigate(`/contracts/${createdContract.id}`);\n      }, 2000);\n    } catch (err) {\n      setError(err.message || 'Đã xảy ra lỗi khi tạo hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      fullScreen: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [error && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(SuccessAlert, {\n      message: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(CustomerContractForm, {\n      contract: contract,\n      onChange: handleContractChange,\n      onSubmit: handleSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateContractPage, \"+R5tk2122bBOaXpf8hn4z/XTfJQ=\", false, function () {\n  return [useNavigate];\n});\n_c = CreateContractPage;\nexport default CreateContractPage;\nvar _c;\n$RefreshReg$(_c, \"CreateContractPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Box", "CustomerContractForm", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "calculateContractAmount", "jsxDEV", "_jsxDEV", "CreateContractPage", "_s", "navigate", "contract", "setContract", "customerId", "startingDate", "endingDate", "totalAmount", "address", "description", "jobDetails", "status", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleContractChange", "updatedContract", "validateContract", "length", "jobDetail", "jobCategoryId", "startDate", "endDate", "workLocation", "workShifts", "workShift", "startTime", "endTime", "numberOfWorkers", "salary", "undefined", "workingDays", "handleSubmit", "calculation", "contractToSubmit", "createdContract", "createContract", "setTimeout", "id", "err", "message", "fullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "onChange", "onSubmit", "_c", "$RefreshReg$"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CreateContractPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { CustomerContract } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\n\nconst CreateContractPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<Partial<CustomerContract>>({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    address: '',\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {\n    setContract(updatedContract);\n  };\n\n  const validateContract = (): boolean => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n\n    if (!contract.address) {\n      setError('Vui lòng nhập địa chỉ hợp đồng');\n      return false;\n    }\n\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workLocation) {\n        setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    setError(null);\n\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n\n    setLoading(true);\n\n    try {\n      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);\n      setSuccess('Hợp đồng đã được tạo thành công!');\n\n      // Redirect to the contract details page after a short delay\n      setTimeout(() => {\n        navigate(`/contracts/${createdContract.id}`);\n      }, 2000);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tạo hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  return (\n    <Box>\n      {error && <ErrorAlert message={error} />}\n      {success && <SuccessAlert message={success} />}\n\n      <CustomerContractForm\n        contract={contract}\n        onChange={handleContractChange}\n        onSubmit={handleSubmit}\n      />\n    </Box>\n  );\n};\n\nexport default CreateContractPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,QAAQ,eAAe;AACnC,SAASC,oBAAoB,QAAQ,wBAAwB;AAE7D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,QAAQ,sBAAsB;AAC/E,SAASC,uBAAuB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAA4B;IAClEgB,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,EAAE;IACdC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,CAAC,CAAC;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EAE3D,MAAM8B,oBAAoB,GAAIC,eAA0C,IAAK;IAC3EhB,WAAW,CAACgB,eAAe,CAAC;EAC9B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAe;IACtC,IAAI,CAAClB,QAAQ,CAACE,UAAU,IAAIF,QAAQ,CAACE,UAAU,KAAK,CAAC,EAAE;MACrDW,QAAQ,CAAC,0BAA0B,CAAC;MACpC,OAAO,KAAK;IACd;;IAEA;;IAEA,IAAI,CAACb,QAAQ,CAACM,OAAO,EAAE;MACrBO,QAAQ,CAAC,gCAAgC,CAAC;MAC1C,OAAO,KAAK;IACd;;IAEA;;IAEA,IAAI,CAACb,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAACW,MAAM,KAAK,CAAC,EAAE;MAC5DN,QAAQ,CAAC,8CAA8C,CAAC;MACxD,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMO,SAAS,IAAIpB,QAAQ,CAACQ,UAAU,EAAE;MAC3C,IAAI,CAACY,SAAS,CAACC,aAAa,IAAID,SAAS,CAACC,aAAa,KAAK,CAAC,EAAE;QAC7DR,QAAQ,CAAC,4DAA4D,CAAC;QACtE,OAAO,KAAK;MACd;MAEA,IAAI,CAACO,SAAS,CAACE,SAAS,EAAE;QACxBT,QAAQ,CAAC,0DAA0D,CAAC;QACpE,OAAO,KAAK;MACd;MAEA,IAAI,CAACO,SAAS,CAACG,OAAO,EAAE;QACtBV,QAAQ,CAAC,2DAA2D,CAAC;QACrE,OAAO,KAAK;MACd;MAEA,IAAI,CAACO,SAAS,CAACI,YAAY,EAAE;QAC3BX,QAAQ,CAAC,+DAA+D,CAAC;QACzE,OAAO,KAAK;MACd;MAEA,IAAI,CAACO,SAAS,CAACK,UAAU,IAAIL,SAAS,CAACK,UAAU,CAACN,MAAM,KAAK,CAAC,EAAE;QAC9DN,QAAQ,CAAC,kEAAkE,CAAC;QAC5E,OAAO,KAAK;MACd;;MAEA;MACA,KAAK,MAAMa,SAAS,IAAIN,SAAS,CAACK,UAAU,EAAE;QAC5C,IAAI,CAACC,SAAS,CAACC,SAAS,EAAE;UACxBd,QAAQ,CAAC,kDAAkD,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA,IAAI,CAACa,SAAS,CAACE,OAAO,EAAE;UACtBf,QAAQ,CAAC,mDAAmD,CAAC;UAC7D,OAAO,KAAK;QACd;QAEA,IAAI,CAACa,SAAS,CAACG,eAAe,IAAIH,SAAS,CAACG,eAAe,IAAI,CAAC,EAAE;UAChEhB,QAAQ,CAAC,qEAAqE,CAAC;UAC/E,OAAO,KAAK;QACd;QAEA,IAAIa,SAAS,CAACI,MAAM,KAAKC,SAAS,IAAIL,SAAS,CAACI,MAAM,GAAG,CAAC,EAAE;UAC1DjB,QAAQ,CAAC,uDAAuD,CAAC;UACjE,OAAO,KAAK;QACd;QAEA,IAAI,CAACa,SAAS,CAACM,WAAW,EAAE;UAC1BnB,QAAQ,CAAC,oDAAoD,CAAC;UAC9D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BpB,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI,CAACK,gBAAgB,CAAC,CAAC,EAAE;MACvB;IACF;;IAEA;IACA,MAAMgB,WAAW,GAAGxC,uBAAuB,CAACM,QAAQ,CAAC;IACrD,MAAMmC,gBAAgB,GAAG;MACvB,GAAGnC,QAAQ;MACXK,WAAW,EAAE6B,WAAW,CAAC7B;IAC3B,CAAC;IAEDM,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMyB,eAAe,GAAG,MAAM9C,eAAe,CAAC+C,cAAc,CAACF,gBAAoC,CAAC;MAClGpB,UAAU,CAAC,kCAAkC,CAAC;;MAE9C;MACAuB,UAAU,CAAC,MAAM;QACfvC,QAAQ,CAAC,cAAcqC,eAAe,CAACG,EAAE,EAAE,CAAC;MAC9C,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjB3B,QAAQ,CAAC2B,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IAC3D,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBAAOd,OAAA,CAACL,cAAc;MAACmD,UAAU;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACtC;EAEA,oBACElD,OAAA,CAACR,GAAG;IAAA2D,QAAA,GACDnC,KAAK,iBAAIhB,OAAA,CAACJ,UAAU;MAACiD,OAAO,EAAE7B;IAAM;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACvChC,OAAO,iBAAIlB,OAAA,CAACH,YAAY;MAACgD,OAAO,EAAE3B;IAAQ;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE9ClD,OAAA,CAACP,oBAAoB;MACnBW,QAAQ,EAAEA,QAAS;MACnBgD,QAAQ,EAAEhC,oBAAqB;MAC/BiC,QAAQ,EAAEhB;IAAa;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAChD,EAAA,CAlJID,kBAA4B;EAAA,QACfV,WAAW;AAAA;AAAA+D,EAAA,GADxBrD,kBAA4B;AAoJlC,eAAeA,kBAAkB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}