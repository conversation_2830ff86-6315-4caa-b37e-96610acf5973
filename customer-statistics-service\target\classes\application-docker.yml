server:
  port: 8089

spring:
  application:
    name: customer-statistics-service
  main:
    allow-bean-definition-overriding: true
  devtools:
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
    livereload:
      enabled: true
    remote:
      secret: mysecret

app:
  customer-service:
    url: http://customer-service:8085
  customer-contract-service:
    url: http://customer-contract-service:8087
  customer-payment-service:
    url: http://customer-payment-service:8088
