[{"D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx": "1", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts": "2", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx": "3", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts": "4", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts": "5", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx": "6", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx": "7", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx": "8", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx": "9", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx": "10", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx": "11", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx": "12", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx": "13", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts": "14", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts": "15", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts": "16", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts": "17", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts": "18", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx": "19", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx": "20", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx": "21", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx": "22", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx": "23", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts": "24", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts": "25", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts": "26", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts": "27", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts": "28", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts": "29", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx": "30", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx": "31", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx": "32", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx": "33", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts": "34", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts": "35", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts": "36", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts": "37", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts": "38", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx": "39", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx": "40", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts": "41", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts": "42", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts": "43", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx": "44", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts": "45", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts": "46", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts": "47", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx": "48", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx": "49", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx": "50", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx": "51", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx": "52", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx": "53", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts": "54", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts": "55", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx": "56", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx": "57", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts": "58", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx": "59", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx": "60", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx": "61", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx": "62", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx": "63", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts": "64", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx": "65"}, {"size": 868, "mtime": 1747975394144, "results": "66", "hashOfConfig": "67"}, {"size": 425, "mtime": 1747901853368, "results": "68", "hashOfConfig": "67"}, {"size": 1793, "mtime": 1747923851728, "results": "69", "hashOfConfig": "67"}, {"size": 468, "mtime": 1747923851728, "results": "70", "hashOfConfig": "67"}, {"size": 138, "mtime": 1747902225279, "results": "71", "hashOfConfig": "67"}, {"size": 3142, "mtime": 1747910578132, "results": "72", "hashOfConfig": "67"}, {"size": 10051, "mtime": 1747931792216, "results": "73", "hashOfConfig": "67"}, {"size": 2913, "mtime": 1747930391001, "results": "74", "hashOfConfig": "67"}, {"size": 4956, "mtime": 1748242435045, "results": "75", "hashOfConfig": "67"}, {"size": 1078, "mtime": 1747902509716, "results": "76", "hashOfConfig": "67"}, {"size": 1836, "mtime": 1747910540518, "results": "77", "hashOfConfig": "67"}, {"size": 571, "mtime": 1747904248056, "results": "78", "hashOfConfig": "67"}, {"size": 605, "mtime": 1747902220275, "results": "79", "hashOfConfig": "67"}, {"size": 4372, "mtime": 1747932197380, "results": "80", "hashOfConfig": "67"}, {"size": 4503, "mtime": 1747930605055, "results": "81", "hashOfConfig": "67"}, {"size": 352, "mtime": 1747975523634, "results": "82", "hashOfConfig": "67"}, {"size": 258, "mtime": 1747902337151, "results": "83", "hashOfConfig": "67"}, {"size": 259, "mtime": 1747927809976, "results": "84", "hashOfConfig": "67"}, {"size": 577, "mtime": 1747902174150, "results": "85", "hashOfConfig": "67"}, {"size": 992, "mtime": 1747902149527, "results": "86", "hashOfConfig": "67"}, {"size": 846, "mtime": 1747902165347, "results": "87", "hashOfConfig": "67"}, {"size": 2240, "mtime": 1747926050567, "results": "88", "hashOfConfig": "67"}, {"size": 1316, "mtime": 1747906323357, "results": "89", "hashOfConfig": "67"}, {"size": 7044, "mtime": 1747927004866, "results": "90", "hashOfConfig": "67"}, {"size": 155, "mtime": 1747901999771, "results": "91", "hashOfConfig": "67"}, {"size": 292, "mtime": 1747998489731, "results": "92", "hashOfConfig": "67"}, {"size": 624, "mtime": 1747976181168, "results": "93", "hashOfConfig": "67"}, {"size": 252, "mtime": 1747922140447, "results": "94", "hashOfConfig": "67"}, {"size": 346, "mtime": 1747902012555, "results": "95", "hashOfConfig": "67"}, {"size": 9760, "mtime": 1747998591179, "results": "96", "hashOfConfig": "67"}, {"size": 13377, "mtime": 1748242446618, "results": "97", "hashOfConfig": "67"}, {"size": 18739, "mtime": 1748242424291, "results": "98", "hashOfConfig": "67"}, {"size": 7624, "mtime": 1748230426622, "results": "99", "hashOfConfig": "67"}, {"size": 3543, "mtime": 1747998520066, "results": "100", "hashOfConfig": "67"}, {"size": 948, "mtime": 1747902060729, "results": "101", "hashOfConfig": "67"}, {"size": 1363, "mtime": 1747907859946, "results": "102", "hashOfConfig": "67"}, {"size": 790, "mtime": 1747906005248, "results": "103", "hashOfConfig": "67"}, {"size": 120, "mtime": 1747907779789, "results": "104", "hashOfConfig": "67"}, {"size": 5514, "mtime": 1747908527895, "results": "105", "hashOfConfig": "67"}, {"size": 12344, "mtime": 1747987012484, "results": "106", "hashOfConfig": "67"}, {"size": 415, "mtime": 1747930255221, "results": "107", "hashOfConfig": "67"}, {"size": 4238, "mtime": 1747931208285, "results": "108", "hashOfConfig": "67"}, {"size": 1351, "mtime": 1747932215756, "results": "109", "hashOfConfig": "67"}, {"size": 8628, "mtime": 1747986997063, "results": "110", "hashOfConfig": "67"}, {"size": 213, "mtime": 1747922856213, "results": "111", "hashOfConfig": "67"}, {"size": 330, "mtime": 1747911990280, "results": "112", "hashOfConfig": "67"}, {"size": 2494, "mtime": 1747986981447, "results": "113", "hashOfConfig": "67"}, {"size": 3892, "mtime": 1747910324864, "results": "114", "hashOfConfig": "67"}, {"size": 9519, "mtime": 1747976446884, "results": "115", "hashOfConfig": "67"}, {"size": 6737, "mtime": 1747976502128, "results": "116", "hashOfConfig": "67"}, {"size": 6283, "mtime": 1747911777952, "results": "117", "hashOfConfig": "67"}, {"size": 2257, "mtime": 1747912064565, "results": "118", "hashOfConfig": "67"}, {"size": 28838, "mtime": 1747994625869, "results": "119", "hashOfConfig": "67"}, {"size": 24610, "mtime": 1747933042688, "results": "120", "hashOfConfig": "67"}, {"size": 368, "mtime": 1747928054242, "results": "121", "hashOfConfig": "67"}, {"size": 2838, "mtime": 1747921471664, "results": "122", "hashOfConfig": "67"}, {"size": 3188, "mtime": 1747932337817, "results": "123", "hashOfConfig": "67"}, {"size": 1849, "mtime": 1747994411130, "results": "124", "hashOfConfig": "67"}, {"size": 6202, "mtime": 1747995320916, "results": "125", "hashOfConfig": "67"}, {"size": 7312, "mtime": 1747994456699, "results": "126", "hashOfConfig": "67"}, {"size": 2066, "mtime": 1747975512424, "results": "127", "hashOfConfig": "67"}, {"size": 3267, "mtime": 1747978131060, "results": "128", "hashOfConfig": "67"}, {"size": 2607, "mtime": 1747994496465, "results": "129", "hashOfConfig": "67"}, {"size": 5887, "mtime": 1748242274081, "results": "130", "hashOfConfig": "67"}, {"size": 7640, "mtime": 1748232092851, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10nwx99", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx", ["327"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts", ["328"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx", ["329", "330", "331", "332"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx", ["333", "334", "335", "336", "337", "338", "339", "340", "341"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx", ["342"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx", ["343"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx", ["344", "345", "346"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx", [], ["347"], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx", ["348", "349", "350", "351"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx", ["352", "353", "354", "355"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx", ["356"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts", ["357", "358"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx", ["359"], [], {"ruleId": "360", "severity": 1, "message": "361", "line": 3, "column": 23, "nodeType": "362", "messageId": "363", "endLine": 3, "endColumn": 33}, {"ruleId": "360", "severity": 1, "message": "364", "line": 18, "column": 7, "nodeType": "362", "messageId": "363", "endLine": 18, "endColumn": 22}, {"ruleId": "360", "severity": 1, "message": "365", "line": 16, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 16, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "366", "line": 17, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 17, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "367", "line": 30, "column": 10, "nodeType": "362", "messageId": "363", "endLine": 30, "endColumn": 28}, {"ruleId": "360", "severity": 1, "message": "368", "line": 30, "column": 30, "nodeType": "362", "messageId": "363", "endLine": 30, "endColumn": 49}, {"ruleId": "360", "severity": 1, "message": "369", "line": 17, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 17, "endColumn": 13}, {"ruleId": "360", "severity": 1, "message": "366", "line": 21, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 21, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "370", "line": 22, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 22, "endColumn": 10}, {"ruleId": "360", "severity": 1, "message": "371", "line": 23, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 23, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "372", "line": 24, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 24, "endColumn": 14}, {"ruleId": "360", "severity": 1, "message": "373", "line": 29, "column": 8, "nodeType": "362", "messageId": "363", "endLine": 29, "endColumn": 18}, {"ruleId": "360", "severity": 1, "message": "374", "line": 30, "column": 8, "nodeType": "362", "messageId": "363", "endLine": 30, "endColumn": 20}, {"ruleId": "360", "severity": 1, "message": "368", "line": 38, "column": 21, "nodeType": "362", "messageId": "363", "endLine": 38, "endColumn": 40}, {"ruleId": "360", "severity": 1, "message": "375", "line": 53, "column": 10, "nodeType": "362", "messageId": "363", "endLine": 53, "endColumn": 26}, {"ruleId": "360", "severity": 1, "message": "376", "line": 15, "column": 8, "nodeType": "362", "messageId": "363", "endLine": 15, "endColumn": 18}, {"ruleId": "360", "severity": 1, "message": "365", "line": 17, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 17, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "369", "line": 7, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 7, "endColumn": 13}, {"ruleId": "360", "severity": 1, "message": "370", "line": 22, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 22, "endColumn": 10}, {"ruleId": "360", "severity": 1, "message": "373", "line": 25, "column": 8, "nodeType": "362", "messageId": "363", "endLine": 25, "endColumn": 18}, {"ruleId": "377", "severity": 1, "message": "378", "line": 592, "column": 6, "nodeType": "379", "endLine": 592, "endColumn": 8, "suggestions": "380", "suppressions": "381"}, {"ruleId": "360", "severity": 1, "message": "382", "line": 1, "column": 17, "nodeType": "362", "messageId": "363", "endLine": 1, "endColumn": 25}, {"ruleId": "360", "severity": 1, "message": "383", "line": 9, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 9, "endColumn": 9}, {"ruleId": "360", "severity": 1, "message": "384", "line": 15, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 15, "endColumn": 12}, {"ruleId": "360", "severity": 1, "message": "385", "line": 17, "column": 10, "nodeType": "362", "messageId": "363", "endLine": 17, "endColumn": 30}, {"ruleId": "360", "severity": 1, "message": "371", "line": 12, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 12, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "372", "line": 13, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 13, "endColumn": 14}, {"ruleId": "360", "severity": 1, "message": "365", "line": 14, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 14, "endColumn": 7}, {"ruleId": "360", "severity": 1, "message": "370", "line": 15, "column": 3, "nodeType": "362", "messageId": "363", "endLine": 15, "endColumn": 10}, {"ruleId": "360", "severity": 1, "message": "386", "line": 3, "column": 10, "nodeType": "362", "messageId": "363", "endLine": 3, "endColumn": 19}, {"ruleId": "360", "severity": 1, "message": "387", "line": 1, "column": 28, "nodeType": "362", "messageId": "363", "endLine": 1, "endColumn": 37}, {"ruleId": "360", "severity": 1, "message": "388", "line": 1, "column": 39, "nodeType": "362", "messageId": "363", "endLine": 1, "endColumn": 48}, {"ruleId": "360", "severity": 1, "message": "389", "line": 25, "column": 35, "nodeType": "362", "messageId": "363", "endLine": 25, "endColumn": 63}, "@typescript-eslint/no-unused-vars", "'Typography' is defined but never used.", "Identifier", "unusedVar", "'checkApiGateway' is assigned a value but never used.", "'Grid' is defined but never used.", "'Chip' is defined but never used.", "'formatDateForInput' is defined but never used.", "'formatDateLocalized' is defined but never used.", "'IconButton' is defined but never used.", "'Divider' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PersonIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'selectedCustomer' is assigned a value but never used.", "'SearchIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCustomerStatistics'. Either include it or remove the dependency array.", "ArrayExpression", ["390"], ["391"], "'useState' is defined but never used.", "'Button' is defined but never used.", "'FormLabel' is defined but never used.", "'formatDateForDisplay' is defined but never used.", "'TextField' is defined but never used.", "'JobDetail' is defined but never used.", "'WorkShift' is defined but never used.", "'ContractCalculationBreakdown' is defined but never used.", {"desc": "392", "fix": "393"}, {"kind": "394", "justification": "395"}, "Update the dependencies array to be: [loadCustomerStatistics]", {"range": "396", "text": "397"}, "directive", "", [22117, 22119], "[loadCustomerStatistics]"]