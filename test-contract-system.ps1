# PowerShell script to test the updated contract system

Write-Host "=== Testing Updated Contract System ===" -ForegroundColor Green

# Step 1: Check if Dock<PERSON> is running
Write-Host "Step 1: Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerStatus = docker ps 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not available. Please install and start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Step 2: Apply database migration
Write-Host "Step 2: Applying database migration..." -ForegroundColor Yellow
try {
    $addColumnQuery = @"
-- Add salary column to work_shifts table if it doesn't exist
ALTER TABLE work_shifts ADD COLUMN IF NOT EXISTS salary DOUBLE PRECISION;

-- Set default value for existing records
UPDATE work_shifts SET salary = 0.0 WHERE salary IS NULL;

-- Verify the column was added
SELECT 'Database migration completed successfully' as result;
"@
    
    docker exec postgres psql -U postgres -d customerContractMSDb -c $addColumnQuery
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database migration completed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to apply database migration" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error applying database migration: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Restart services
Write-Host "Step 3: Restarting services..." -ForegroundColor Yellow
$services = @("customer-contract-service", "api-gateway")

foreach ($service in $services) {
    Write-Host "Restarting $service..." -ForegroundColor Cyan
    docker restart $service
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $service restarted successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Failed to restart $service" -ForegroundColor Yellow
    }
}

# Step 4: Wait for services to start
Write-Host "Step 4: Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 25

# Step 5: Test service health
Write-Host "Step 5: Testing service health..." -ForegroundColor Yellow

$healthChecks = @{
    "Customer Service" = "http://localhost:8085/actuator/health"
    "Job Service" = "http://localhost:8086/actuator/health"
    "Customer Contract Service" = "http://localhost:8087/actuator/health"
    "Customer Payment Service" = "http://localhost:8088/actuator/health"
    "Customer Statistics Service" = "http://localhost:8089/actuator/health"
    "API Gateway" = "http://localhost:8083/actuator/health"
}

foreach ($service in $healthChecks.Keys) {
    try {
        $response = Invoke-RestMethod -Uri $healthChecks[$service] -Method Get -TimeoutSec 10
        if ($response.status -eq "UP") {
            Write-Host "✅ $service is healthy" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $service health check returned: $($response.status)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Could not reach $service health endpoint" -ForegroundColor Red
    }
}

# Step 6: Test API endpoints
Write-Host "Step 6: Testing API endpoints..." -ForegroundColor Yellow

$apiTests = @{
    "GET /api/customer" = "http://localhost:8083/api/customer"
    "GET /api/job-category" = "http://localhost:8083/api/job-category"
    "GET /api/customer-contract" = "http://localhost:8083/api/customer-contract"
}

foreach ($test in $apiTests.Keys) {
    try {
        $response = Invoke-RestMethod -Uri $apiTests[$test] -Method Get -TimeoutSec 10
        Write-Host "✅ $test - Success" -ForegroundColor Green
    } catch {
        Write-Host "❌ $test - Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Contract System Test Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Updated Features:" -ForegroundColor Cyan
Write-Host "1. ✅ Automatic contract amount calculation based on work shifts" -ForegroundColor White
Write-Host "2. ✅ Automatic contract dates calculation from job details" -ForegroundColor White
Write-Host "3. ✅ Detailed work schedule display by day of week" -ForegroundColor White
Write-Host "4. ✅ Salary field added to work shifts" -ForegroundColor White
Write-Host "5. ✅ Real-time calculation updates" -ForegroundColor White
Write-Host "6. ✅ Comprehensive validation" -ForegroundColor White
Write-Host ""
Write-Host "How to test the new features:" -ForegroundColor Yellow
Write-Host "1. Navigate to http://localhost:3000/contracts/create" -ForegroundColor White
Write-Host "2. Select a customer" -ForegroundColor White
Write-Host "3. Enter contract address and description" -ForegroundColor White
Write-Host "4. Add job details with:" -ForegroundColor White
Write-Host "   - Job category selection" -ForegroundColor Gray
Write-Host "   - Work location" -ForegroundColor Gray
Write-Host "   - Start and end dates for the job" -ForegroundColor Gray
Write-Host "   - Work shifts with salary, workers, and working days" -ForegroundColor Gray
Write-Host "5. Observe automatic calculations:" -ForegroundColor White
Write-Host "   - Contract dates auto-calculated from job dates" -ForegroundColor Gray
Write-Host "   - Total amount auto-calculated from work shifts" -ForegroundColor Gray
Write-Host "   - Detailed calculation breakdown displayed" -ForegroundColor Gray
Write-Host "6. Submit the contract and view detailed schedule" -ForegroundColor White
Write-Host ""
Write-Host "The system now provides a complete workflow as described in your requirements!" -ForegroundColor Green
