{"ast": null, "code": "import { calculateWorkingDates } from './workingDaysUtils';\n\n/**\n * Calculate the total contract amount based on work shifts and contract dates\n * @param contract The customer contract with job details and work shifts\n * @returns Object containing total amount and calculation breakdown\n */\nexport const calculateContractAmount = contract => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      totalAmount: 0,\n      breakdown: [],\n      summary: {\n        totalWorkShifts: 0,\n        totalWorkers: 0,\n        totalWorkingDays: 0,\n        contractDuration: 0\n      }\n    };\n  }\n  const breakdown = [];\n  let totalAmount = 0;\n  let totalWorkShifts = 0;\n  let totalWorkers = 0;\n  let totalWorkingDays = 0;\n\n  // Calculate contract duration in days\n  const contractDuration = contract.startingDate && contract.endingDate ? Math.ceil((new Date(contract.endingDate).getTime() - new Date(contract.startingDate).getTime()) / (1000 * 60 * 60 * 24)) + 1 : 0;\n  contract.jobDetails.forEach((jobDetail, jobIndex) => {\n    if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) return;\n    const jobBreakdown = {\n      jobDetailIndex: jobIndex,\n      jobCategoryName: jobDetail.jobCategoryName || `Công việc ${jobIndex + 1}`,\n      workShifts: [],\n      jobTotal: 0\n    };\n    jobDetail.workShifts.forEach((workShift, shiftIndex) => {\n      if (!workShift.salary || !workShift.numberOfWorkers || !workShift.workingDays) return;\n\n      // Use job detail dates if available, otherwise use contract dates\n      const startDate = jobDetail.startDate || contract.startingDate;\n      const endDate = jobDetail.endDate || contract.endingDate;\n      if (!startDate || !endDate) return;\n\n      // Calculate actual working dates for this shift\n      const workingDates = calculateWorkingDates(startDate, endDate, workShift.workingDays);\n      const workingDaysCount = workingDates.length;\n\n      // Calculate amount for this shift\n      const shiftAmount = workShift.salary * workShift.numberOfWorkers * workingDaysCount;\n      const shiftBreakdown = {\n        shiftIndex,\n        startTime: workShift.startTime,\n        endTime: workShift.endTime,\n        salary: workShift.salary,\n        numberOfWorkers: workShift.numberOfWorkers,\n        workingDaysCount,\n        workingDates,\n        shiftAmount\n      };\n      jobBreakdown.workShifts.push(shiftBreakdown);\n      jobBreakdown.jobTotal += shiftAmount;\n      totalAmount += shiftAmount;\n      totalWorkShifts++;\n      totalWorkers += workShift.numberOfWorkers;\n      totalWorkingDays += workingDaysCount;\n    });\n    if (jobBreakdown.workShifts.length > 0) {\n      breakdown.push(jobBreakdown);\n    }\n  });\n  return {\n    totalAmount,\n    breakdown,\n    summary: {\n      totalWorkShifts,\n      totalWorkers,\n      totalWorkingDays,\n      contractDuration\n    }\n  };\n};\n\n/**\n * Interface for contract calculation breakdown\n */\n\n/**\n * Interface for work shift calculation breakdown\n */\n\n/**\n * Interface for contract calculation summary\n */\n\n/**\n * Format calculation breakdown for display\n * @param breakdown The calculation breakdown\n * @returns Formatted string for display\n */\nexport const formatCalculationBreakdown = breakdown => {\n  if (breakdown.length === 0) return 'Chưa có thông tin tính toán';\n  let result = 'Chi tiết tính toán:\\n\\n';\n  breakdown.forEach((job, index) => {\n    result += `${job.jobCategoryName}:\\n`;\n    job.workShifts.forEach((shift, shiftIndex) => {\n      result += `  Ca ${shiftIndex + 1} (${shift.startTime} - ${shift.endTime}):\\n`;\n      result += `    Lương: ${shift.salary.toLocaleString('vi-VN')} VNĐ/ca\\n`;\n      result += `    Số người: ${shift.numberOfWorkers}\\n`;\n      result += `    Số ngày làm việc: ${shift.workingDaysCount}\\n`;\n      result += `    Thành tiền: ${shift.shiftAmount.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n    });\n    result += `  Tổng ${job.jobCategoryName}: ${job.jobTotal.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n  });\n  return result;\n};\n\n/**\n * Calculate contract start and end dates automatically from job details\n * @param contract The customer contract with job details\n * @returns Object containing calculated start and end dates\n */\nexport const calculateContractDates = contract => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      startingDate: '',\n      endingDate: ''\n    };\n  }\n  let earliestStart = '';\n  let latestEnd = '';\n  contract.jobDetails.forEach(jobDetail => {\n    if (jobDetail.startDate) {\n      if (!earliestStart || jobDetail.startDate < earliestStart) {\n        earliestStart = jobDetail.startDate;\n      }\n    }\n    if (jobDetail.endDate) {\n      if (!latestEnd || jobDetail.endDate > latestEnd) {\n        latestEnd = jobDetail.endDate;\n      }\n    }\n  });\n  return {\n    startingDate: earliestStart,\n    endingDate: latestEnd\n  };\n};", "map": {"version": 3, "names": ["calculateWorkingDates", "calculateContractAmount", "contract", "jobDetails", "length", "totalAmount", "breakdown", "summary", "totalWorkShifts", "totalWorkers", "totalWorkingDays", "contractDuration", "startingDate", "endingDate", "Math", "ceil", "Date", "getTime", "for<PERSON>ach", "jobDetail", "jobIndex", "workShifts", "jobBreakdown", "jobDetailIndex", "jobCategoryName", "jobTotal", "workShift", "shiftIndex", "salary", "numberOfWorkers", "workingDays", "startDate", "endDate", "workingDates", "workingDaysCount", "shiftAmount", "shiftBreakdown", "startTime", "endTime", "push", "formatCalculationBreakdown", "result", "job", "index", "shift", "toLocaleString", "calculateContractDates", "earliestStart", "latestEnd"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/utils/contractCalculationUtils.ts"], "sourcesContent": ["import { CustomerContract, JobDetail, WorkShift } from '../models';\nimport { calculateWorkingDates } from './workingDaysUtils';\n\n/**\n * Calculate the total contract amount based on work shifts and contract dates\n * @param contract The customer contract with job details and work shifts\n * @returns Object containing total amount and calculation breakdown\n */\nexport const calculateContractAmount = (contract: Partial<CustomerContract>): {\n  totalAmount: number;\n  breakdown: ContractCalculationBreakdown[];\n  summary: ContractCalculationSummary;\n} => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      totalAmount: 0,\n      breakdown: [],\n      summary: {\n        totalWorkShifts: 0,\n        totalWorkers: 0,\n        totalWorkingDays: 0,\n        contractDuration: 0\n      }\n    };\n  }\n\n  const breakdown: ContractCalculationBreakdown[] = [];\n  let totalAmount = 0;\n  let totalWorkShifts = 0;\n  let totalWorkers = 0;\n  let totalWorkingDays = 0;\n\n  // Calculate contract duration in days\n  const contractDuration = contract.startingDate && contract.endingDate\n    ? Math.ceil((new Date(contract.endingDate).getTime() - new Date(contract.startingDate).getTime()) / (1000 * 60 * 60 * 24)) + 1\n    : 0;\n\n  contract.jobDetails.forEach((jobDetail, jobIndex) => {\n    if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) return;\n\n    const jobBreakdown: ContractCalculationBreakdown = {\n      jobDetailIndex: jobIndex,\n      jobCategoryName: jobDetail.jobCategoryName || `Công việc ${jobIndex + 1}`,\n      workShifts: [],\n      jobTotal: 0\n    };\n\n    jobDetail.workShifts.forEach((workShift, shiftIndex) => {\n      if (!workShift.salary || !workShift.numberOfWorkers || !workShift.workingDays) return;\n\n      // Use job detail dates if available, otherwise use contract dates\n      const startDate = jobDetail.startDate || contract.startingDate;\n      const endDate = jobDetail.endDate || contract.endingDate;\n\n      if (!startDate || !endDate) return;\n\n      // Calculate actual working dates for this shift\n      const workingDates = calculateWorkingDates(startDate, endDate, workShift.workingDays);\n      const workingDaysCount = workingDates.length;\n\n      // Calculate amount for this shift\n      const shiftAmount = workShift.salary * workShift.numberOfWorkers * workingDaysCount;\n\n      const shiftBreakdown: WorkShiftCalculationBreakdown = {\n        shiftIndex,\n        startTime: workShift.startTime,\n        endTime: workShift.endTime,\n        salary: workShift.salary,\n        numberOfWorkers: workShift.numberOfWorkers,\n        workingDaysCount,\n        workingDates,\n        shiftAmount\n      };\n\n      jobBreakdown.workShifts.push(shiftBreakdown);\n      jobBreakdown.jobTotal += shiftAmount;\n      totalAmount += shiftAmount;\n      totalWorkShifts++;\n      totalWorkers += workShift.numberOfWorkers;\n      totalWorkingDays += workingDaysCount;\n    });\n\n    if (jobBreakdown.workShifts.length > 0) {\n      breakdown.push(jobBreakdown);\n    }\n  });\n\n  return {\n    totalAmount,\n    breakdown,\n    summary: {\n      totalWorkShifts,\n      totalWorkers,\n      totalWorkingDays,\n      contractDuration\n    }\n  };\n};\n\n/**\n * Interface for contract calculation breakdown\n */\nexport interface ContractCalculationBreakdown {\n  jobDetailIndex: number;\n  jobCategoryName: string;\n  workShifts: WorkShiftCalculationBreakdown[];\n  jobTotal: number;\n}\n\n/**\n * Interface for work shift calculation breakdown\n */\nexport interface WorkShiftCalculationBreakdown {\n  shiftIndex: number;\n  startTime: string;\n  endTime: string;\n  salary: number;\n  numberOfWorkers: number;\n  workingDaysCount: number;\n  workingDates: string[];\n  shiftAmount: number;\n}\n\n/**\n * Interface for contract calculation summary\n */\nexport interface ContractCalculationSummary {\n  totalWorkShifts: number;\n  totalWorkers: number;\n  totalWorkingDays: number;\n  contractDuration: number;\n}\n\n/**\n * Format calculation breakdown for display\n * @param breakdown The calculation breakdown\n * @returns Formatted string for display\n */\nexport const formatCalculationBreakdown = (breakdown: ContractCalculationBreakdown[]): string => {\n  if (breakdown.length === 0) return 'Chưa có thông tin tính toán';\n\n  let result = 'Chi tiết tính toán:\\n\\n';\n\n  breakdown.forEach((job, index) => {\n    result += `${job.jobCategoryName}:\\n`;\n\n    job.workShifts.forEach((shift, shiftIndex) => {\n      result += `  Ca ${shiftIndex + 1} (${shift.startTime} - ${shift.endTime}):\\n`;\n      result += `    Lương: ${shift.salary.toLocaleString('vi-VN')} VNĐ/ca\\n`;\n      result += `    Số người: ${shift.numberOfWorkers}\\n`;\n      result += `    Số ngày làm việc: ${shift.workingDaysCount}\\n`;\n      result += `    Thành tiền: ${shift.shiftAmount.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n    });\n\n    result += `  Tổng ${job.jobCategoryName}: ${job.jobTotal.toLocaleString('vi-VN')} VNĐ\\n\\n`;\n  });\n\n  return result;\n};\n\n/**\n * Calculate contract start and end dates automatically from job details\n * @param contract The customer contract with job details\n * @returns Object containing calculated start and end dates\n */\nexport const calculateContractDates = (contract: Partial<CustomerContract>): {\n  startingDate: string;\n  endingDate: string;\n} => {\n  if (!contract.jobDetails || contract.jobDetails.length === 0) {\n    return {\n      startingDate: '',\n      endingDate: ''\n    };\n  }\n\n  let earliestStart: string = '';\n  let latestEnd: string = '';\n\n  contract.jobDetails.forEach(jobDetail => {\n    if (jobDetail.startDate) {\n      if (!earliestStart || jobDetail.startDate < earliestStart) {\n        earliestStart = jobDetail.startDate;\n      }\n    }\n\n    if (jobDetail.endDate) {\n      if (!latestEnd || jobDetail.endDate > latestEnd) {\n        latestEnd = jobDetail.endDate;\n      }\n    }\n  });\n\n  return {\n    startingDate: earliestStart,\n    endingDate: latestEnd\n  };\n};\n"], "mappings": "AACA,SAASA,qBAAqB,QAAQ,oBAAoB;;AAE1D;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAIC,QAAmC,IAItE;EACH,IAAI,CAACA,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO;MACLC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE;QACPC,eAAe,EAAE,CAAC;QAClBC,YAAY,EAAE,CAAC;QACfC,gBAAgB,EAAE,CAAC;QACnBC,gBAAgB,EAAE;MACpB;IACF,CAAC;EACH;EAEA,MAAML,SAAyC,GAAG,EAAE;EACpD,IAAID,WAAW,GAAG,CAAC;EACnB,IAAIG,eAAe,GAAG,CAAC;EACvB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,gBAAgB,GAAG,CAAC;;EAExB;EACA,MAAMC,gBAAgB,GAAGT,QAAQ,CAACU,YAAY,IAAIV,QAAQ,CAACW,UAAU,GACjEC,IAAI,CAACC,IAAI,CAAC,CAAC,IAAIC,IAAI,CAACd,QAAQ,CAACW,UAAU,CAAC,CAACI,OAAO,CAAC,CAAC,GAAG,IAAID,IAAI,CAACd,QAAQ,CAACU,YAAY,CAAC,CAACK,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAC5H,CAAC;EAELf,QAAQ,CAACC,UAAU,CAACe,OAAO,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;IACnD,IAAI,CAACD,SAAS,CAACE,UAAU,IAAIF,SAAS,CAACE,UAAU,CAACjB,MAAM,KAAK,CAAC,EAAE;IAEhE,MAAMkB,YAA0C,GAAG;MACjDC,cAAc,EAAEH,QAAQ;MACxBI,eAAe,EAAEL,SAAS,CAACK,eAAe,IAAI,aAAaJ,QAAQ,GAAG,CAAC,EAAE;MACzEC,UAAU,EAAE,EAAE;MACdI,QAAQ,EAAE;IACZ,CAAC;IAEDN,SAAS,CAACE,UAAU,CAACH,OAAO,CAAC,CAACQ,SAAS,EAAEC,UAAU,KAAK;MACtD,IAAI,CAACD,SAAS,CAACE,MAAM,IAAI,CAACF,SAAS,CAACG,eAAe,IAAI,CAACH,SAAS,CAACI,WAAW,EAAE;;MAE/E;MACA,MAAMC,SAAS,GAAGZ,SAAS,CAACY,SAAS,IAAI7B,QAAQ,CAACU,YAAY;MAC9D,MAAMoB,OAAO,GAAGb,SAAS,CAACa,OAAO,IAAI9B,QAAQ,CAACW,UAAU;MAExD,IAAI,CAACkB,SAAS,IAAI,CAACC,OAAO,EAAE;;MAE5B;MACA,MAAMC,YAAY,GAAGjC,qBAAqB,CAAC+B,SAAS,EAAEC,OAAO,EAAEN,SAAS,CAACI,WAAW,CAAC;MACrF,MAAMI,gBAAgB,GAAGD,YAAY,CAAC7B,MAAM;;MAE5C;MACA,MAAM+B,WAAW,GAAGT,SAAS,CAACE,MAAM,GAAGF,SAAS,CAACG,eAAe,GAAGK,gBAAgB;MAEnF,MAAME,cAA6C,GAAG;QACpDT,UAAU;QACVU,SAAS,EAAEX,SAAS,CAACW,SAAS;QAC9BC,OAAO,EAAEZ,SAAS,CAACY,OAAO;QAC1BV,MAAM,EAAEF,SAAS,CAACE,MAAM;QACxBC,eAAe,EAAEH,SAAS,CAACG,eAAe;QAC1CK,gBAAgB;QAChBD,YAAY;QACZE;MACF,CAAC;MAEDb,YAAY,CAACD,UAAU,CAACkB,IAAI,CAACH,cAAc,CAAC;MAC5Cd,YAAY,CAACG,QAAQ,IAAIU,WAAW;MACpC9B,WAAW,IAAI8B,WAAW;MAC1B3B,eAAe,EAAE;MACjBC,YAAY,IAAIiB,SAAS,CAACG,eAAe;MACzCnB,gBAAgB,IAAIwB,gBAAgB;IACtC,CAAC,CAAC;IAEF,IAAIZ,YAAY,CAACD,UAAU,CAACjB,MAAM,GAAG,CAAC,EAAE;MACtCE,SAAS,CAACiC,IAAI,CAACjB,YAAY,CAAC;IAC9B;EACF,CAAC,CAAC;EAEF,OAAO;IACLjB,WAAW;IACXC,SAAS;IACTC,OAAO,EAAE;MACPC,eAAe;MACfC,YAAY;MACZC,gBAAgB;MAChBC;IACF;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;;AAQA;AACA;AACA;;AAYA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM6B,0BAA0B,GAAIlC,SAAyC,IAAa;EAC/F,IAAIA,SAAS,CAACF,MAAM,KAAK,CAAC,EAAE,OAAO,6BAA6B;EAEhE,IAAIqC,MAAM,GAAG,yBAAyB;EAEtCnC,SAAS,CAACY,OAAO,CAAC,CAACwB,GAAG,EAAEC,KAAK,KAAK;IAChCF,MAAM,IAAI,GAAGC,GAAG,CAAClB,eAAe,KAAK;IAErCkB,GAAG,CAACrB,UAAU,CAACH,OAAO,CAAC,CAAC0B,KAAK,EAAEjB,UAAU,KAAK;MAC5Cc,MAAM,IAAI,QAAQd,UAAU,GAAG,CAAC,KAAKiB,KAAK,CAACP,SAAS,MAAMO,KAAK,CAACN,OAAO,MAAM;MAC7EG,MAAM,IAAI,cAAcG,KAAK,CAAChB,MAAM,CAACiB,cAAc,CAAC,OAAO,CAAC,WAAW;MACvEJ,MAAM,IAAI,iBAAiBG,KAAK,CAACf,eAAe,IAAI;MACpDY,MAAM,IAAI,yBAAyBG,KAAK,CAACV,gBAAgB,IAAI;MAC7DO,MAAM,IAAI,mBAAmBG,KAAK,CAACT,WAAW,CAACU,cAAc,CAAC,OAAO,CAAC,UAAU;IAClF,CAAC,CAAC;IAEFJ,MAAM,IAAI,UAAUC,GAAG,CAAClB,eAAe,KAAKkB,GAAG,CAACjB,QAAQ,CAACoB,cAAc,CAAC,OAAO,CAAC,UAAU;EAC5F,CAAC,CAAC;EAEF,OAAOJ,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,sBAAsB,GAAI5C,QAAmC,IAGrE;EACH,IAAI,CAACA,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACC,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO;MACLQ,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE;IACd,CAAC;EACH;EAEA,IAAIkC,aAAqB,GAAG,EAAE;EAC9B,IAAIC,SAAiB,GAAG,EAAE;EAE1B9C,QAAQ,CAACC,UAAU,CAACe,OAAO,CAACC,SAAS,IAAI;IACvC,IAAIA,SAAS,CAACY,SAAS,EAAE;MACvB,IAAI,CAACgB,aAAa,IAAI5B,SAAS,CAACY,SAAS,GAAGgB,aAAa,EAAE;QACzDA,aAAa,GAAG5B,SAAS,CAACY,SAAS;MACrC;IACF;IAEA,IAAIZ,SAAS,CAACa,OAAO,EAAE;MACrB,IAAI,CAACgB,SAAS,IAAI7B,SAAS,CAACa,OAAO,GAAGgB,SAAS,EAAE;QAC/CA,SAAS,GAAG7B,SAAS,CAACa,OAAO;MAC/B;IACF;EACF,CAAC,CAAC;EAEF,OAAO;IACLpB,YAAY,EAAEmC,aAAa;IAC3BlC,UAAU,EAAEmC;EACd,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}