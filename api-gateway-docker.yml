spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      routes:
        - id: customer-service
          uri: http://customer-service:8085
          predicates:
            - Path=/api/customer/**
        - id: worker-service
          uri: http://worker-service:8080
          predicates:
            - Path=/api/worker/**
        - id: job-schedule-service
          uri: http://job-schedule-service:8081
          predicates:
            - Path=/api/job-schedule/**
        - id: register-service
          uri: http://register-service:8082
          predicates:
            - Path=/api/register/**
        - id: worker-contract-service
          uri: http://worker-contract-service:8084
          predicates:
            - Path=/api/worker-contract/**
        - id: job-service
          uri: http://job-service:8086
          predicates:
            - Path=/api/job/**,/api/job-category/**
        - id: customer-contract-service
          uri: http://customer-contract-service:8087
          predicates:
            - Path=/api/customer-contract/**
        - id: customer-payment-service
          uri: http://customer-payment-service:8088
          predicates:
            - Path=/api/customer-payment/**
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
server:
  port: 8083

management:
  endpoints:
    web:
      exposure:
        include: "*"
