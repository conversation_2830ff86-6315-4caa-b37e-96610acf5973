import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Grid,
  useTheme,
  Divider,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import EventIcon from '@mui/icons-material/Event';
import { WorkShift, JobDetail } from '../../models';
import { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/currencyUtils';

// Mapping for Vietnamese day names
const dayNames: { [key: number]: string } = {
  1: 'Thứ Hai',
  2: 'Thứ Ba',
  3: 'Th<PERSON> Tư',
  4: 'Th<PERSON>',
  5: 'Th<PERSON>',
  6: '<PERSON><PERSON><PERSON>',
  7: '<PERSON><PERSON>'
};

interface WorkingDatesPreviewProps {
  workShift: WorkShift;
  jobDetail: JobDetail;
  shiftIndex: number;
}

const WorkingDatesPreview: React.FC<WorkingDatesPreviewProps> = ({
  workShift,
  jobDetail,
  shiftIndex
}) => {
  const [expanded, setExpanded] = useState(false);
  const theme = useTheme();

  // Calculate working dates for this shift
  const workingDates = calculateWorkingDates(
    jobDetail.startDate,
    jobDetail.endDate,
    workShift.workingDays
  );

  // Group dates by day of week for better display
  const datesByDayOfWeek: { [key: number]: string[] } = {};
  workingDates.forEach(dateStr => {
    const date = new Date(dateStr.split('/').reverse().join('-')); // Convert DD/MM/YYYY to YYYY-MM-DD
    const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7
    if (!datesByDayOfWeek[dayOfWeek]) {
      datesByDayOfWeek[dayOfWeek] = [];
    }
    datesByDayOfWeek[dayOfWeek].push(dateStr);
  });

  // Calculate total amount for this shift
  const totalAmount = workShift.salary && workShift.numberOfWorkers && workingDates.length
    ? workShift.salary * workShift.numberOfWorkers * workingDates.length
    : 0;

  if (!workShift.workingDays || !jobDetail.startDate || !jobDetail.endDate) {
    return (
      <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.warning.light }}>
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            Vui lòng chọn ngày làm việc và thời gian để xem lịch làm việc chi tiết
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card variant="outlined" sx={{ mb: 2, borderColor: theme.palette.primary.light }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
            Lịch làm việc Ca {shiftIndex + 1} ({workShift.startTime} - {workShift.endTime})
          </Typography>
        </Box>

        {/* Summary Information */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} sm={3}>
            <Typography variant="body2" color="text.secondary">Ngày làm việc</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {formatWorkingDays(workShift.workingDays)}
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="body2" color="text.secondary">Tổng số ngày</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium', color: theme.palette.primary.main }}>
              {workingDates.length} ngày
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="body2" color="text.secondary">Số nhân công</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {workShift.numberOfWorkers} người
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="body2" color="text.secondary">Tổng tiền ca</Typography>
            <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
              {formatCurrency(totalAmount)}
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ mb: 2 }} />

        {/* Detailed working dates */}
        <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)} sx={{ boxShadow: 'none' }}>
          <AccordionSummary 
            expandIcon={<ExpandMoreIcon />}
            sx={{ 
              backgroundColor: theme.palette.action.hover,
              borderRadius: '4px',
              minHeight: '36px',
              '& .MuiAccordionSummary-content': { margin: '4px 0' }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <EventIcon fontSize="small" sx={{ mr: 1 }} />
              <Typography variant="body2">
                Lịch làm việc chi tiết ({workingDates.length} ngày)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ pt: 2 }}>
            {Object.entries(datesByDayOfWeek)
              .sort(([a], [b]) => parseInt(a) - parseInt(b))
              .map(([dayOfWeek, dates]) => (
                <Box key={dayOfWeek} sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                    {dayNames[parseInt(dayOfWeek)]}:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {dates.map((date, index) => (
                      <Chip
                        key={index}
                        label={date}
                        size="small"
                        variant="outlined"
                        color="primary"
                        sx={{ fontSize: '0.75rem' }}
                      />
                    ))}
                  </Box>
                </Box>
              ))}

            {workingDates.length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                Không có ngày làm việc nào trong khoảng thời gian đã chọn
              </Typography>
            )}
          </AccordionDetails>
        </Accordion>

        {/* Calculation breakdown */}
        {totalAmount > 0 && (
          <Box sx={{ mt: 2, p: 2, backgroundColor: theme.palette.success.light, borderRadius: '4px' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Tính toán chi tiết:
            </Typography>
            <Typography variant="body2">
              {formatCurrency(workShift.salary || 0)} × {workShift.numberOfWorkers} người × {workingDates.length} ngày = {formatCurrency(totalAmount)}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default WorkingDatesPreview;
